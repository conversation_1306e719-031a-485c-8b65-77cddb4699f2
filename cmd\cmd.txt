ztl@cf-d588-test:~$ cat /proc/cpuinfo
processor       : 0
BogoMIPS        : 48.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd03
CPU revision    : 4

processor       : 1
BogoMIPS        : 48.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd03
CPU revision    : 4

processor       : 2
BogoMIPS        : 48.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd03
CPU revision    : 4

processor       : 3
BogoMIPS        : 48.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd03
CPU revision    : 4

processor       : 4
BogoMIPS        : 48.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd08
CPU revision    : 2

processor       : 5
BogoMIPS        : 48.00
Features        : fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer : 0x41
CPU architecture: 8
CPU variant     : 0x0
CPU part        : 0xd08
CPU revision    : 2

Serial          : fc5ab0397fc6b369

ztl@cf-d588-test:~$ uname -a
Linux cf-d588-test 4.4.189 #433 SMP Wed Jan 8 17:26:20 CST 2025 aarch64 aarch64 aarch64 GNU/Linux


ztl@cf-d588-test:~$ ls -la /dev/video*
-rw-rw---- 1 <USER> <GROUP> 4 May 23 15:17 /dev/video-dec0
-rw-rw---- 1 <USER> <GROUP> 4 May 23 15:17 /dev/video-enc0

ztl@cf-d588-test:~$ v4l2-ctl --list-devices
Cannot open device /dev/video0, exiting.

ztl@cf-d588-test:~$ v4l2-ctl -d /dev/video-dec0 --all
Unable to detect what device /dev/video-dec0 is, exiting.

ztl@cf-d588-test:~$ ffmpeg -hwaccels
ffmpeg version 4.2.7-0ubuntu0.1 Copyright (c) 2000-2022 the FFmpeg developers
  built with gcc 9 (Ubuntu 9.4.0-1ubuntu1~20.04.1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  WARNING: library configuration mismatch
  avcodec     configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared --enable-version3 --disable-doc --disable-programs --enable-libaribb24 --enable-liblensfun --enable-libopencore_amrnb --enable-libopencore_amrwb --enable-libtesseract --enable-libvo_amrwbenc
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
Hardware acceleration methods:
vdpau
vaapi
drm
opencl

ztl@cf-d588-test:~$ ffmpeg -codecs | grep -i hevc
ffmpeg version 4.2.7-0ubuntu0.1 Copyright (c) 2000-2022 the FFmpeg developers
  built with gcc 9 (Ubuntu 9.4.0-1ubuntu1~20.04.1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  WARNING: library configuration mismatch
  avcodec     configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared --enable-version3 --disable-doc --disable-programs --enable-libaribb24 --enable-liblensfun --enable-libopencore_amrnb --enable-libopencore_amrwb --enable-libtesseract --enable-libvo_amrwbenc
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
 DEV.L. hevc                 H.265 / HEVC (High Efficiency Video Coding) (decoders: hevc hevc_v4l2m2m ) (encoders: libx265 hevc_v4l2m2m hevc_vaapi )

 ztl@cf-d588-test:~$ ls -la /dev/video*
-rw-rw---- 1 <USER> <GROUP> 4 May 23 15:17 /dev/video-dec0
-rw-rw---- 1 <USER> <GROUP> 4 May 23 15:17 /dev/video-enc0

ztl@cf-d588-test:~$ ls -la /dev/video-dec*
-rw-rw---- 1 <USER> <GROUP> 4 May 23 15:17 /dev/video-dec0

ztl@cf-d588-test:~$ ls -la /dev/video-dec0
-rw-rw---- 1 <USER> <GROUP> 4 May 23 15:17 /dev/video-dec0

ztl@cf-d588-test:~$ v4l2-ctl -d /dev/video-dec0 --list-formats
Unable to detect what device /dev/video-dec0 is, exiting.

ztl@cf-d588-test:~$ udevadm info -a -n /dev/video-dec0
Unknown device "/dev/video-dec0": Inappropriate ioctl for device

ztl@cf-d588-test:~$ ls -l /sys/class/video4linux/
total 0

ztl@cf-d588-test:~$ stat /dev/video-dec0
  File: /dev/video-dec0
  Size: 4               Blocks: 8          IO Block: 4096   regular file
Device: 6h/6d   Inode: 21765       Links: 1
Access: (0660/-rw-rw----)  Uid: (    0/    root)   Gid: (   44/   video)
Access: 2025-05-23 15:17:59.039000044 +0800
Modify: 2025-05-23 15:17:59.039000044 +0800
Change: 2025-05-23 15:17:59.174000044 +0800
 Birth: -

ztl@cf-d588-test:~$ readlink -f /sys/class/video4linux/video-dec0
/sys/class/video4linux/video-dec0

ztl@cf-d588-test:~/Videos$ ffmpeg -v verbose -stats \
>   -rtsp_transport tcp \
>   -i "rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0" \
>   -c:v copy -an -t 10 \
>   -f null - \
>   2> video_dec0_rtsp_test.log
ztl@cf-d588-test:~/Videos$ ll
total 9
drwxr-xr-x  2 <USER> <GROUP> 1024 May 23 15:49 ./
drwxr-xr-x 18 <USER> <GROUP> 1024 May 23 15:18 ../
-rw-rw-r--  1 <USER> <GROUP> 6331 May 23 15:49 video_dec0_rtsp_test.log
ztl@cf-d588-test:~/Videos$ cat video_dec0_rtsp_test.log 
ffmpeg version 4.2.7-0ubuntu0.1 Copyright (c) 2000-2022 the FFmpeg developers
  built with gcc 9 (Ubuntu 9.4.0-1ubuntu1~20.04.1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  WARNING: library configuration mismatch
  avcodec     configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared --enable-version3 --disable-doc --disable-programs --enable-libaribb24 --enable-liblensfun --enable-libopencore_amrnb --enable-libopencore_amrwb --enable-libtesseract --enable-libvo_amrwbenc
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
[tcp @ 0x558dd8e880] Starting connection attempt to *********** port 28554
[tcp @ 0x558dd8e880] Successfully connected to *********** port 28554
[rtsp @ 0x558ddc9c90] SDP:
v=0
o=- 0 0 IN IP4 127.0.0.1
s=Session streamed with GStreamer
c=IN IP4 0.0.0.0
t=0 0
m=video 0 RTP/AVP 96
a=control:rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0/trackID=0
a=rtpmap:96 H265/90000
a=fmtp:96 sprop-pps=RAHArPAzJA==; sprop-sps=QgEBAUAAAAMAAAMAAAMAAAMAeKADwIARB8uW9KQhGT8C; sprop-vps=QAEMAf//AUAAAAMAAAMAAAMAAAMAeLwJ      

[rtsp @ 0x558ddc9c90] setting jitter buffer size to 0
Input #0, rtsp, from 'rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0':
  Metadata:
    title           : Session streamed with GStreamer
  Duration: N/A, start: 0.153756, bitrate: N/A
    Stream #0:0: Video: hevc (Main), 1 reference frame, yuv420p(tv), 1920x1080 (1920x1088), 30 tbr, 90k tbn, 90k tbc
Output #0, null, to 'pipe:':
  Metadata:
    title           : Session streamed with GStreamer
    encoder         : Lavf58.29.100
    Stream #0:0: Video: hevc (Main), 1 reference frame, yuv420p(tv), 1920x1080 (0x0), q=2-31, 30 tbr, 90k tbn, 90k tbc
Stream mapping:
  Stream #0:0 -> #0:0 (copy)
Press [q] to stop, [?] for help
No more output streams to write to, finishing.:09.60 bitrate=N/A speed=1.08x
frame=  289 fps= 31 q=-1.0 Lsize=N/A time=00:00:09.98 bitrate=N/A speed=1.07x
video:3054kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: unknown
Input file #0 (rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0):
  Input stream #0:0 (video): 302 packets read (3208210 bytes);
  Total: 302 packets (3208210 bytes) demuxed
Output file #0 (pipe:):
  Output stream #0:0 (video): 289 packets muxed (3127795 bytes);
  Total: 289 packets (3127795 bytes) muxed






####################################################
  



  

   ffmpeg -v verbose -stats \
  -rtsp_transport tcp \
  -c:v hevc_v4l2m2m \
  -i "rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0" \
  -an -t 10 \
  -f null - \
  2> v4l2m2m_decode_rtsp.log

  ffmpeg -v verbose -stats \
  -hwaccel vaapi \
  -hwaccel_device /dev/dri/renderD128 \
  -hwaccel_output_format vaapi \
  -rtsp_transport tcp \
  -i "rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0" \
  -an -t 10 \
  -f null - \
  2> vaapi_decode_rtsp.log

 ffmpeg -v verbose -stats \
  -hwaccel drm \
  -hwaccel_device /dev/dri/card0 \
  -rtsp_transport tcp \
  -i "rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0" \
  -an -t 10 \
  -f null - \
  2> drm_decode_rtsp.log

  ztl@cf-d588-test:~/Videos$ cat v4l2m2m_decode_rtsp.log 
ffmpeg version 4.2.7-0ubuntu0.1 Copyright (c) 2000-2022 the FFmpeg developers
  built with gcc 9 (Ubuntu 9.4.0-1ubuntu1~20.04.1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  WARNING: library configuration mismatch
  avcodec     configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared --enable-version3 --disable-doc --disable-programs --enable-libaribb24 --enable-liblensfun --enable-libopencore_amrnb --enable-libopencore_amrwb --enable-libtesseract --enable-libvo_amrwbenc
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
[tcp @ 0x55933a1880] Starting connection attempt to *********** port 28554
[tcp @ 0x55933a1880] Successfully connected to *********** port 28554
[rtsp @ 0x55933dcc70] SDP:
v=0
o=- 0 0 IN IP4 127.0.0.1
s=Session streamed with GStreamer
c=IN IP4 0.0.0.0
t=0 0
m=video 0 RTP/AVP 96
a=control:rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0/trackID=0
a=rtpmap:96 H265/90000
a=fmtp:96 sprop-pps=RAHArPAzJA==; sprop-sps=QgEBAUAAAAMAAAMAAAMAAAMAeKADwIARB8uW9KQhGT8C; sprop-vps=QAEMAf//AUAAAAMAAAMAAAMAAAMAeLwJ        

[rtsp @ 0x55933dcc70] setting jitter buffer size to 0
Input #0, rtsp, from 'rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0':
  Metadata:
    title           : Session streamed with GStreamer
  Duration: N/A, start: 0.168011, bitrate: N/A
    Stream #0:0: Video: hevc (Main), 1 reference frame, yuv420p(tv), 1920x1080 (1920x1088), 90k tbr, 90k tbn, 90k tbc
[AVBSFContext @ 0x55933dcb80] The input looks like it is Annex B already
[hevc_v4l2m2m @ 0x5593464690] Could not find a valid device
[hevc_v4l2m2m @ 0x5593464690] can't configure decoder
Stream mapping:
  Stream #0:0 -> #0:0 (hevc (hevc_v4l2m2m) -> wrapped_avframe (native))
Error while opening decoder for input stream #0:0 : Operation not permitted

ztl@cf-d588-test:~/Videos$ cat vaapi_decode_rtsp.log 
ffmpeg version 4.2.7-0ubuntu0.1 Copyright (c) 2000-2022 the FFmpeg developers
  built with gcc 9 (Ubuntu 9.4.0-1ubuntu1~20.04.1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  WARNING: library configuration mismatch
  avcodec     configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared --enable-version3 --disable-doc --disable-programs --enable-libaribb24 --enable-liblensfun --enable-libopencore_amrnb --enable-libopencore_amrwb --enable-libtesseract --enable-libvo_amrwbenc
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
[tcp @ 0x55898f2880] Starting connection attempt to *********** port 28554
[tcp @ 0x55898f2880] Successfully connected to *********** port 28554
[rtsp @ 0x558992dcc0] SDP:
v=0
o=- 0 0 IN IP4 127.0.0.1
s=Session streamed with GStreamer
c=IN IP4 0.0.0.0
t=0 0
m=video 0 RTP/AVP 96
a=control:rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0/trackID=0
a=rtpmap:96 H265/90000
a=fmtp:96 sprop-pps=RAHArPAzJA==; sprop-sps=QgEBAUAAAAMAAAMAAAMAAAMAeKADwIARB8uW9KQhGT8C; sprop-vps=QAEMAf//AUAAAAMAAAMAAAMAAAMAeLwJ        

[rtsp @ 0x558992dcc0] setting jitter buffer size to 0
Input #0, rtsp, from 'rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0':
  Metadata:
    title           : Session streamed with GStreamer
  Duration: N/A, start: 0.161544, bitrate: N/A
    Stream #0:0: Video: hevc (Main), 1 reference frame, yuv420p(tv), 1920x1080 (1920x1088), 90k tbr, 90k tbn, 90k tbc
[AVHWDeviceContext @ 0x558992db40] libva: VA-API version 1.7.0
[AVHWDeviceContext @ 0x558992db40] libva: vaGetDriverNameByIndex() failed with unknown libva error, driver_name = (null)
[AVHWDeviceContext @ 0x558992db40] Failed to initialise VAAPI connection: -1 (unknown libva error).
Device creation failed: -5.
[hevc @ 0x55899a19d0] No device available for decoder: device type vaapi needed for codec hevc.
Stream mapping:
  Stream #0:0 -> #0:0 (hevc (native) -> wrapped_avframe (native))
Device setup failed for decoder on input stream #0:0 : Input/output error

ztl@cf-d588-test:~/Videos$ cat drm_decode_rtsp.log 
ffmpeg version 4.2.7-0ubuntu0.1 Copyright (c) 2000-2022 the FFmpeg developers
  built with gcc 9 (Ubuntu 9.4.0-1ubuntu1~20.04.1)
  configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared
  WARNING: library configuration mismatch
  avcodec     configuration: --prefix=/usr --extra-version=0ubuntu0.1 --toolchain=hardened --libdir=/usr/lib/aarch64-linux-gnu --incdir=/usr/include/aarch64-linux-gnu --arch=arm64 --enable-gpl --disable-stripping --enable-avresample --disable-filter=resample --enable-avisynth --enable-gnutls --enable-ladspa --enable-libaom --enable-libass --enable-libbluray --enable-libbs2b --enable-libcaca --enable-libcdio --enable-libcodec2 --enable-libflite --enable-libfontconfig --enable-libfreetype --enable-libfribidi --enable-libgme --enable-libgsm --enable-libjack --enable-libmp3lame --enable-libmysofa --enable-libopenjpeg --enable-libopenmpt --enable-libopus --enable-libpulse --enable-librsvg --enable-librubberband --enable-libshine --enable-libsnappy --enable-libsoxr --enable-libspeex --enable-libssh --enable-libtheora --enable-libtwolame --enable-libvidstab --enable-libvorbis --enable-libvpx --enable-libwavpack --enable-libwebp --enable-libx265 --enable-libxml2 --enable-libxvid --enable-libzmq --enable-libzvbi --enable-lv2 --enable-omx --enable-openal --enable-opencl --enable-opengl --enable-sdl2 --enable-libdc1394 --enable-libdrm --enable-libiec61883 --enable-chromaprint --enable-frei0r --enable-libx264 --enable-shared --enable-version3 --disable-doc --disable-programs --enable-libaribb24 --enable-liblensfun --enable-libopencore_amrnb --enable-libopencore_amrwb --enable-libtesseract --enable-libvo_amrwbenc
  libavutil      56. 31.100 / 56. 31.100
  libavcodec     58. 54.100 / 58. 54.100
  libavformat    58. 29.100 / 58. 29.100
  libavdevice    58.  8.100 / 58.  8.100
  libavfilter     7. 57.100 /  7. 57.100
  libavresample   4.  0.  0 /  4.  0.  0
  libswscale      5.  5.100 /  5.  5.100
  libswresample   3.  5.100 /  3.  5.100
  libpostproc    55.  5.100 / 55.  5.100
[tcp @ 0x55761e9880] Starting connection attempt to *********** port 28554
[tcp @ 0x55761e9880] Successfully connected to *********** port 28554
[rtsp @ 0x5576224cb0] SDP:
v=0
o=- 0 0 IN IP4 127.0.0.1
s=Session streamed with GStreamer
c=IN IP4 0.0.0.0
t=0 0
m=video 0 RTP/AVP 96
a=control:rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0/trackID=0
a=rtpmap:96 H265/90000
a=fmtp:96 sprop-pps=RAHArPAzJA==; sprop-sps=QgEBAUAAAAMAAAMAAAMAAAMAeKADwIARB8uW9KQhGT8C; sprop-vps=QAEMAf//AUAAAAMAAAMAAAMAAAMAeLwJ        

[rtsp @ 0x5576224cb0] setting jitter buffer size to 0
Input #0, rtsp, from 'rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0':
  Metadata:
    title           : Session streamed with GStreamer
  Duration: N/A, start: 0.145144, bitrate: N/A
    Stream #0:0: Video: hevc (Main), 1 reference frame, yuv420p(tv), 1920x1080 (1920x1088), 90k tbr, 90k tbn, 90k tbc
[AVHWDeviceContext @ 0x5576224b40] Opened DRM device /dev/dri/card0: driver rockchip version 1.0.0.
Stream mapping:
  Stream #0:0 -> #0:0 (hevc (native) -> wrapped_avframe (native))
Press [q] to stop, [?] for help
[hevc @ 0x55762431b0] Could not find ref with POC 15
[hevc @ 0x55762431b0] Could not find ref with POC 14
[graph 0 input from stream 0:0 @ 0x55767dbe80] w:1920 h:1080 pixfmt:yuv420p tb:1/90000 fr:90000/1 sar:0/1 sws_param:flags=2
Output #0, null, to 'pipe:':
  Metadata:
    title           : Session streamed with GStreamer
    encoder         : Lavf58.29.100
    Stream #0:0: Video: wrapped_avframe, 1 reference frame, yuv420p, 1920x1080, q=2-31, 200 kb/s, 90k fps, 90k tbn, 90k tbc
    Metadata:
      encoder         : Lavc58.54.100 wrapped_avframe
No more output streams to write to, finishing.:09.75 bitrate=N/A speed=1.05x
frame=  302 fps= 31 q=-0.0 Lsize=N/A time=00:00:09.99 bitrate=N/A speed=1.04x
video:158kB audio:0kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: unknown
Input file #0 (rtsp://api.builderx.com:28554/live/pro/test_zjFMJxkJ/stream0):
  Input stream #0:0 (video): 309 packets read (3239383 bytes); 304 frames decoded;
  Total: 309 packets (3239383 bytes) demuxed
Output file #0 (pipe:):
  Output stream #0:0 (video): 302 frames encoded; 302 packets muxed (161872 bytes);
  Total: 302 packets (161872 bytes) muxed