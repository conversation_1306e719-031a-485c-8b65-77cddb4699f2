import { ipc } from "./ipcRenderer";
import notificationService from "./notificationService";
import router from "@/router";

/**
 * WebSocket消息处理器类
 */
export class WsMessageHandler {
  constructor() {
    this.handlers = new Map();
  }

  /**
   * 注册消息处理函数
   * @param {number} page - 页面编号
   * @param {Function} callback - 回调函数
   * @returns {string} - 处理器ID
   */
  register(page, callback) {
    const handlerId = `${page}_${Date.now()}`;
    this.handlers.set(handlerId, { page, callback });
    return handlerId;
  }

  /**
   * 注销消息处理函数
   * @param {string} handlerId - 处理器ID
   */
  unregister(handlerId) {
    this.handlers.delete(handlerId);
  }

  /**
   * 处理WebSocket消息
   * @param {Event} event - 事件对象
   * @param {Object} msg - 消息对象
   */
  handleMessage(event, msg) {
    console.log("handleMessage", msg);
    switch (msg["id"]) {
      case 11:
        switch (msg.padStatus) {
          case 8:
          case 32:
            // 等待页面
            router.push("/hello");
            break;
          
          // 选车判断
          default:
            break;
        }
      case 20:
        this.handlers.forEach((handler) => {
          console.log("handler", handler, msg);
          switch (handler.page) {
            case 310:
              handler.callback(event, msg);
              break;

            case 300:
              notificationService.success("成功同步操作台数据");
              break;

            case "*":
              handler.callback(event, msg);
              break;

            default:
              break;
          }
        });

        break;

      default:

        break;
    }
  }
}

// 创建单例实例
const wsMessageHandler = new WsMessageHandler();

/**
 * 初始化WebSocket消息监听
 */
export const initWsMessageListener = () => {
  ipc.on("ipc-message", (event, msg) => {
    wsMessageHandler.handleMessage(event, msg);
  });
};

/**
 * 注册WebSocket消息处理函数
 * @param {string|'*'} page - 页面编号，'*'表示所有类型
 * @param {Function} callback - 回调函数
 * @returns {string} - 处理器ID，用于注销
 */
export const registerWsHandler = (page, callback) => {
  return wsMessageHandler.register(page, callback);
};

/**
 * 注销WebSocket消息处理函数
 * @param {string} handlerId - 处理器ID
 */
export const unregisterWsHandler = (handlerId) => {
  wsMessageHandler.unregister(handlerId);
};
