**故障点归纳**

| 日志片段                                                         | 说明                                             |
| ------------------------------------------------------------ | ---------------------------------------------- |
| `mpp: unable to create dec h265 for soc unknown unsupported` | MPP 在本 SoC 上找不到 H.265 硬件解码器而直接返回 *unsupported* |
| `hevc_rkmpp Failed to init MPP context: -1`                  | FFmpeg 调用同一套 MPP 接口，初始化同样失败                    |
| `method SETUP failed: 461 Unsupported Transport`             | FFmpeg 先试 UDP 被摄像机拒绝，随即回退到 TCP，此警告可忽略          |

**根因**

> RK3399 的一代 rkvdec 主线驱动目前只支持 H.264/VP9，HEVC 补丁仍停留在邮件列表；Ubuntu/Armbian 里自带的 libmpp 亦未为 RK3399 编译 HEVC 路径，因此任何经 *rkmpp* 或 *mppvideodec* 走硬解的命令都会报 unsupported 而退出。([wiki.pine64.org][1])

---

## 可选解决方案

| 方案                       | 指令示例                                                                                                                                                                 | 适用场景               | 优缺点                                      |
| ------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------ | ---------------------------------------- |
| **1. 纯软件解码**             | `gst-launch-1.0 rtspsrc location=rtsp://… latency=200 ! rtph265depay ! h265parse ! avdec_h265 ! autovideosink`<br>`ffmpeg -rtsp_transport tcp -i rtsp://… -f null -` | 单路 1080 p 或帧率要求不高  | 无需改系统，CPU 占用增加 (RK3399 ≈ 2-3 core/1080p) |
| **2. 服务器/摄像机端转码为 H.264** | `ffmpeg -i rtsp://src -c:v libx264 -preset veryfast -tune zerolatency -f rtsp rtsp://board/stream264`                                                                | 多路监看、CPU 吃紧        | 板端可继续用 H.264 硬解；画质略降，延时↑                 |
| **3. 编译 HEVC 补丁链**       | - 升级内核 ≥ 6.1 并打入 *rkvdec-hevc* 补丁<br>- 编译最新 libmpp + v4l2-request FFmpeg/GStreamer<br>- 解码命令：`ffmpeg -hwaccel drm -i …` 或 `gst-launch-1.0 … v4l2slh265dec …`         | 技术验证、对 RK3399 情有独钟 | 需自己维护内核/库；社区补丁仍在 review                  |
| **4. 换硬件**               | RK3566/3568、RK3588、i.MX8MP、Jetson 等                                                                                                                                  | 长期、大规模 4 K HEVC    | 一次性投入，但最稳                                |

> ‼ 若只想先看画面，建议从方案 1 开始；在监控/多路场景下，一般“摄像机直接推 H.264”或“服务器实时转码”是最省事的折中。

---

### 常用排障心得

1. **确认传输协议**
   若想消除 461 警告，可显式指定 TCP：

   ```bash
   ffmpeg -rtsp_transport tcp -i rtsp://...
   ```

2. **检查 CPU 负载**

   ```bash
   top -p $(pgrep -f ffmpeg)        # 实时观察
   ```

3. **GStreamer 插件选择**

   * avdec\_h265 → 纯软件
   * v4l2slh265dec → 内核 v4l2-request 驱动
   * mppvideodec → Rockchip 专用硬解，RK3399 无 HEVC

4. **LIBRARY 版本对齐**
   如果自行编译 MPP/FFmpeg，请确保：

   * MPP ≥ rkvdec-hevc 补丁分支
   * FFmpeg 需 `--enable-rkmpp`（旧路径）或 `--enable-v4l2-request`（主线方案）

---

### 参考

* Pine64 主线硬解现状：RK3399 仅 H.264/VP9，HEVC 补丁待合并([wiki.pine64.org][1])
* 461 Unsupported Transport 行为说明（ffmpeg 仍会继续回落 TCP）([Red5][2])

如需进一步协助（补丁编译步骤、服务器转码脚本优化等），欢迎继续交流。

[1]: https://wiki.pine64.org/wiki/Mainline_Hardware_Decoding "Mainline Hardware Decoding - PINE64"
[2]: https://www.red5.net/docs/red5-pro/users-guide/protocols/third-party-publishers/red5-pro-ffmpeg-reading-rtsp-streams/?utm_source=chatgpt.com "Reading RTSP Streams - Red5 Pro"
