<template>
  <div class="d-flex">
    <!-- 左侧垂直选项卡 -->
    <div class="tabs-container">
      <v-tabs v-model="tab" color="primary" direction="vertical" class="intelligence-tabs">
        <v-tab v-for="item in tabItems.filter((item) => !item.disable)" :key="item.value" :value="item.value">
          {{ item.title }}
        </v-tab>
      </v-tabs>
    </div>

    <!-- 右侧功能区域 -->
    <div class="content-container">
      <v-card class="content-card">
        <!-- 行人识别 -->
        <div v-if="settings.pedestrian.enabled" id="pedestrian" class="section-container">
          <div class="section-header">
            <span class="section-title">行人识别</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.pedestrian.switch"
              ></v-checkbox>
            </div>
          </div>
        </div>

        <!-- 姿态检测 -->
        <div v-if="settings.posture.enabled" id="posture" class="section-container">
          <div class="section-header">
            <span class="section-title">姿态检测</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.posture.switch"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">角度过大报警音</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.posture.alarmSound"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">方案选择</div>
              <div class="d-flex align-center">
                <span class="scheme-label">方案A</span>
                <v-switch
                  hide-details
                  density="compact"
                  color="primary"
                  class="mx-2"
                  :model-value="!settings.posture.tiltScheme[0].isUse"
                  @update:model-value="
                    (val) => {
                      settings.posture.tiltScheme[0].isUse = !val;
                      settings.posture.tiltScheme[1].isUse = val;
                    }
                  "
                ></v-switch>
                <span class="scheme-label">方案B</span>
              </div>
            </div>
            <div class="setting-item" v-if="settings.posture.tiltScheme[0].isUse">
              <div class="setting-label">预警值设置(A)</div>
              <div class="d-flex flex-row items-center">
                <div class="d-flex align-center">
                  <span class="slider-label">X轴预警值:</span>
                  <div class="w-[300px]">
                    <v-slider
                      v-model="settings.posture.tiltScheme[0].warmXValue"
                      :min="settings.posture.tiltScheme[0].minXValue"
                      :max="settings.posture.tiltScheme[0].maxXValue"
                      hide-details
                      :step="0.1"
                      thumb-label="always"
                      color="primary"
                    ></v-slider>
                  </div>
                </div>
                <div class="d-flex align-center">
                  <span class="slider-label">Y轴预警值:</span>
                  <div class="w-[300px]">
                    <v-slider
                      v-model="settings.posture.tiltScheme[0].warmYValue"
                      :min="settings.posture.tiltScheme[0].minYValue"
                      :max="settings.posture.tiltScheme[0].maxYValue"
                      hide-details
                      :step="0.1"
                      thumb-label="always"
                      color="primary"
                    ></v-slider>
                  </div>
                </div>
              </div>
            </div>
            <div class="setting-item" v-if="settings.posture.tiltScheme[1].isUse">
              <div class="setting-label">预警值设置(B)</div>
              <div class="d-flex flex-row items-center">
                <div class="d-flex align-center">
                  <span class="slider-label">X轴预警值:</span>
                  <div class="w-[300px]">
                    <v-slider
                      v-model="settings.posture.tiltScheme[1].warmXValue"
                      :min="settings.posture.tiltScheme[1].minXValue"
                      :max="settings.posture.tiltScheme[1].maxXValue"
                      hide-details
                      :step="0.1"
                      thumb-label="always"
                      class="mx-2"
                      color="primary"
                    ></v-slider>
                  </div>
                </div>
                <div class="d-flex align-center">
                  <span class="slider-label">Y轴预警值:</span>
                  <div class="w-[300px]">
                    <v-slider
                      v-model="settings.posture.tiltScheme[1].warmYValue"
                      :min="settings.posture.tiltScheme[1].minYValue"
                      :max="settings.posture.tiltScheme[1].maxYValue"
                      hide-details
                      :step="0.1"
                      thumb-label="always"
                      class="mx-2"
                      color="primary"
                    ></v-slider>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 地面平整度 -->
        <div v-if="settings.terrain.enabled" id="terrain" class="section-container">
          <div class="section-header">
            <span class="section-title">地面平整度</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.terrain.switch"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">投影颜色</div>
              <div class="color-options">
                <div
                  v-for="(color, index) in colorOptions"
                  :key="index"
                  class="color-option"
                  :class="{ selected: settings.terrain.color === color }"
                  :style="{ backgroundColor: color }"
                  @click="settings.terrain.color = color"
                ></div>
              </div>
            </div>
            <div class="setting-item">
              <div class="setting-label">不透明度</div>
              <div class="d-flex align-center opacity-control">
                <span>0%</span>
                <v-slider
                  v-model="settings.terrain.opacity"
                  min="0"
                  max="100"
                  hide-details
                  class="mx-2"
                  color="primary"
                ></v-slider>
                <span>100%</span>
                <div class="opacity-controls">
                  <v-btn
                    icon="mdi-minus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="decrementOpacity('terrain')"
                  ></v-btn>
                  <v-btn
                    icon="mdi-plus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="incrementOpacity('terrain')"
                  ></v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧剖面雷达 -->
        <div v-if="settings.sideRadar.enabled" id="sideRadar" class="section-container">
          <div class="section-header">
            <span class="section-title">侧剖面雷达</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.sideRadar.switch"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">投影颜色</div>
              <div class="color-options">
                <div
                  v-for="(color, index) in colorOptions"
                  :key="index"
                  class="color-option"
                  :class="{ selected: settings.sideRadar.color === color }"
                  :style="{ backgroundColor: color }"
                  @click="settings.sideRadar.color = color"
                ></div>
              </div>
            </div>
            <div class="setting-item">
              <div class="setting-label">不透明度</div>
              <div class="d-flex align-center opacity-control">
                <span>0%</span>
                <v-slider
                  v-model="settings.sideRadar.opacity"
                  min="0"
                  max="100"
                  hide-details
                  class="mx-2"
                  color="primary"
                ></v-slider>
                <span>100%</span>
                <div class="opacity-controls">
                  <v-btn
                    icon="mdi-minus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="decrementOpacity('sideRadar')"
                  ></v-btn>
                  <v-btn
                    icon="mdi-plus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="incrementOpacity('sideRadar')"
                  ></v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 防碰撞预警 -->
        <div v-if="settings.collisionPrevention.enabled" id="collisionPrevention" class="section-container">
          <div class="section-header">
            <span class="section-title">防碰撞预警</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.collisionPrevention.switch"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">距离过近报警音</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.collisionPrevention.distanceAlarm"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">车尾防碰撞制停</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.collisionPrevention.autoStop"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">预警值设置</div>
              <div class="d-flex flex-row items-center">
                <div class="d-flex align-center">
                  <span class="slider-label">雷达距离(红色值):</span>
                  <v-number-input
                    class="w-[200px]"
                    v-model="settings.collisionPrevention.radarRedValue"
                    :min="0"
                    :max="settings.collisionPrevention.radarYellowValue"
                    control-variant="split"
                    :precision="1"
                    :step="0.1"
                    hide-details
                  />
                </div>

                <div class="d-flex align-center">
                  <span class="slider-label">雷达距离(黄色值):</span>
                  <v-number-input
                    class="w-[200px]"
                    v-model="settings.collisionPrevention.radarYellowValue"
                    :min="settings.collisionPrevention.radarRedValue"
                    :max="settings.collisionPrevention.radarGreenValue"
                    control-variant="split"
                    :precision="1"
                    :step="0.1"
                    hide-details
                  />
                </div>
                <div class="d-flex align-center">
                  <span class="slider-label">雷达距离(绿值):</span>
                  <v-number-input
                    class="w-[200px]"
                    v-model="settings.collisionPrevention.radarGreenValue"
                    :min="settings.collisionPrevention.radarYellowValue"
                    :max="20"
                    control-variant="split"
                    :precision="1"
                    :step="0.1"
                    hide-details
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 铲斗落点 -->
        <div v-if="settings.bucketLanding.enabled" id="bucketLanding" class="section-container">
          <div class="section-header">
            <span class="section-title">铲斗落点</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.bucketLanding.switch"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">投影形态</div>
              <div class="d-flex align-center">
                <v-radio-group v-model="settings.bucketLanding.type" inline hide-details>
                  <v-radio label="铲斗面" :value="1" color="primary"></v-radio>
                  <v-radio label="斗齿线" :value="0" color="primary"></v-radio>
                </v-radio-group>
              </div>
            </div>
            <div class="setting-item">
              <div class="setting-label">投影颜色</div>
              <div class="color-options">
                <div
                  v-for="(color, index) in colorOptions"
                  :key="index"
                  class="color-option"
                  :class="{ selected: settings.bucketLanding.color === color }"
                  :style="{ backgroundColor: color }"
                  @click="settings.bucketLanding.color = color"
                ></div>
              </div>
            </div>
            <div class="setting-item">
              <div class="setting-label">不透明度</div>
              <div class="d-flex align-center opacity-control">
                <span>0%</span>
                <v-slider
                  v-model="settings.bucketLanding.opacity"
                  min="0"
                  max="100"
                  hide-details
                  class="mx-2"
                  color="primary"
                ></v-slider>
                <span>100%</span>
                <div class="opacity-controls">
                  <v-btn
                    icon="mdi-minus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="decrementOpacity('bucketLanding')"
                  ></v-btn>
                  <v-btn
                    icon="mdi-plus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="incrementOpacity('bucketLanding')"
                  ></v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 斗齿识别 -->
        <div v-if="settings.bucketTooth.enabled" id="bucketTooth" class="section-container">
          <div class="section-header">
            <span class="section-title">斗齿识别</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.bucketTooth.switch"
              ></v-checkbox>
            </div>
          </div>
        </div>

        <!-- 粉尘透视 -->
        <div v-if="settings.dust.enabled" id="dust" class="section-container">
          <div class="section-header">
            <span class="section-title">粉尘透视</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.dust.switch"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">不透明度</div>
              <div class="d-flex align-center opacity-control">
                <span>0%</span>
                <v-slider
                  v-model="settings.dust.opacity"
                  min="0"
                  max="100"
                  hide-details
                  class="mx-2"
                  color="primary"
                ></v-slider>
                <span>100%</span>
                <div class="opacity-controls">
                  <v-btn
                    icon="mdi-minus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="decrementOpacity('dust')"
                  ></v-btn>
                  <v-btn
                    icon="mdi-plus"
                    color="primary"
                    variant="text"
                    density="compact"
                    @click="incrementOpacity('dust')"
                  ></v-btn>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 远程上电 -->
        <div v-if="settings.remotePower.enabled" id="remotePower" class="section-container">
          <div class="section-header">
            <span class="section-title">远程上电</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">上电开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.remotePower.powerOn"
              ></v-checkbox>
            </div>
            <div class="setting-item">
              <div class="setting-label">下电开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.remotePower.powerOff"
              ></v-checkbox>
            </div>
          </div>
        </div>

        <!-- 自动复位 -->
        <div v-if="settings.autoReset.enabled" id="autoReset" class="section-container">
          <div class="section-header">
            <span class="section-title">自动复位</span>
            <span class="section-permanent">永久</span>
          </div>
          <div class="section-content">
            <div class="setting-item">
              <div class="setting-label">功能开关</div>
              <v-checkbox
                hide-details
                density="compact"
                color="primary"
                class="setting-control"
                v-model="settings.autoReset.switch"
              ></v-checkbox>
            </div>
          </div>
        </div>
      </v-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick, onMounted, onBeforeUnmount } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { useGoTo } from "vuetify";
import Keyboard from "@/components/Keyboard/index.vue";
import { cloneDeep, isEqual } from "lodash-es";
import { toFrontendFormat, toServerFormat } from "@/utils/intelligenceAdapter";
import { sendParams2Android } from "@/utils/androidMessage";
import { findDifferences } from "@/utils/diffUtils";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";

const goTo = useGoTo(); // 初始化工具函数
const tab = ref("regular"); // 选项卡状态
let prevSettings = null; // 上一次的配置

// 标签页配置
const tabItems = [
  { value: "pedestrian", title: "行人识别", icon: "mdi-walk", disable: false },
  { value: "posture", title: "姿态监测", icon: "mdi-human-male-height", disable: false },
  { value: "terrain", title: "地面平整度", icon: "mdi-terrain", disable: false },
  { value: "sideRadar", title: "侧剖面雷达", icon: "mdi-radar", disable: false },
  { value: "collisionPrevention", title: "防碰撞预警", icon: "mdi-car-brake-alert", disable: false },
  { value: "bucketLanding", title: "铲斗落点", icon: "mdi-shield", disable: false },
  { value: "bucketTooth", title: "斗齿识别", icon: "mdi-tooth", disable: false },
  { value: "dust", title: "粉尘透视", icon: "mdi-weather-fog", disable: false },
  { value: "remotePower", title: "远程上电", icon: "mdi-power-plug", disable: false },
  { value: "autoReset", title: "自动复位", icon: "mdi-refresh-auto", disable: false },
];

// 颜色选项配置
const colorOptions = ["#FF0000", "#00FF00", "#0000FF", "#FFFF00", "#FF00FF", "#00FFFF"];

let initData = {};

// 设置状态管理
const settings = reactive({
  // 行人识别
  pedestrian: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
  },
  // 姿态检测
  posture: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
    alarmSound: true, // 报警音
    tiltScheme: [
      {
        isUse: true, // 是否启用
        name: "A", // 平台名称
        minXValue: 5, // X轴最小值
        warmXValue: 0, // X轴预警值
        maxXValue: 45, // X轴最大值
        minYValue: 5, // Y轴最小值
        warmYValue: 0, // Y轴预警值
        maxYValue: 45, // Y轴最大值
      },
      {
        isUse: true, // 是否启用
        name: "B", // 平台名称
        minXValue: 5, // X轴最小值
        maxXValue: 45, // X轴最大值
        warmXValue: 0, // X轴预警值
        maxYValue: 45, // Y轴最大值
        minYValue: 5, // Y轴最小值
        warmYValue: 0, // Y轴预警值
      },
    ],
  },
  // 地面平整度
  terrain: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
    color: "#00FF00", // 投影颜色
    opacity: 0, // 不透明度
  },
  // 侧剖面雷达
  sideRadar: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
    color: "#00FF00", // 投影颜色
    opacity: 0, // 不透明度
  },
  // 防碰撞预警
  collisionPrevention: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
    distanceAlarm: true, // 距离过近报警音
    autoStop: true, // 车尾防碰撞制停开关
    radarGreenValue: 0, // 雷达绿色阈值
    radarRedValue: 0, // 雷达红色阈值
    radarYellowValue: 0, // 雷达黄色阈值
  },
  // 铲斗落点
  bucketLanding: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
    type: 0, // 投影形态 1-铲斗面 0-斗齿线
    color: "#00FF00", // 投影颜色
    opacity: 0, // 不透明度
  },
  // 斗齿识别
  bucketTooth: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
  },
  // 粉尘透视
  dust: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
    opacity: 0, // 不透明度
  },
  // 远程上电
  remotePower: {
    enabled: true, // 是否有此功能
    powerOn: true, // 上电开关
    powerOff: true, // 下电开关
  },
  // 自动复位
  autoReset: {
    enabled: true, // 是否有此功能
    switch: true, // 功能开关
  },
});

const getInitialSettings = async () => {
  initData = await ipc.invoke("global-state:get", "intelligent");
  let data = cloneDeep(toFrontendFormat(initData));
  Object.keys(settings).forEach((key) => {
    if (data[key]) {
      Object.assign(settings[key], data[key]);
    }
  });
};

/**
 * 不透明度调整函数
 * @param {string} section - 设置区域
 * @param {number} step - 调整步长
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 */
const adjustOpacity = (section, step, min = 0, max = 100) => {
  const newValue = settings[section].opacity + step;
  if (newValue >= min && newValue <= max) {
    settings[section].opacity = newValue;
  }
};

// 减小不透明度
const decrementOpacity = (section) => adjustOpacity(section, -10);

// 增加不透明度
const incrementOpacity = (section) => adjustOpacity(section, 10);

/**
 * 处理导航跳转
 * @param {string} path - 目标元素ID
 */
const handleNavigation = async (path) => {
  // 如果滚动锁定中，不执行滚动操作
  if (isScrollLocked.value) return;

  // 设置tab切换锁定标志，防止触发滚动监听
  isTabChangeLocked.value = true;

  await nextTick(() => {
    const el = document.getElementById(path);
    if (el) {
      goTo(el, {
        offset: -20,
        duration: 500,
        container: ".content-container",
      });
    } else {
      console.error("未找到目标元素:", path);
    }

    // 500ms后解除锁定，与滚动动画时长一致
    setTimeout(() => {
      isTabChangeLocked.value = false;
    }, 500);
  });
};

// 监听选项卡变化并执行导航
watch(
  () => tab.value,
  (newVal) => handleNavigation(newVal)
);

watch(
  () => settings,
  (newVal) => {
    ipc.send("global-state:set", {
      key: "intelligent",
      value: toServerFormat(newVal, initData),
    });

    let diffList = findDifferences(toServerFormat(newVal, initData), toServerFormat(prevSettings, initData));
    if (diffList.length > 0) {
      diffList.forEach((item) => {
        sendParams2Android("intelligent." + item.path, item.newValue);
      });
    }
    prevSettings = cloneDeep(newVal);
  },
  {
    deep: true,
  }
);

// 创建锁定标志，防止滚动和tab切换相互触发
const isScrollLocked = ref(false);
const isTabChangeLocked = ref(false);

// 创建IntersectionObserver实例
let observer = null;

// 初始化IntersectionObserver
const initIntersectionObserver = () => {
  const options = {
    root: document.querySelector(".content-container"),
    rootMargin: "-30px 0px -100% 0px",
    offset: 0,
    threshold: 0, // 当目标元素可见时触发回调
  };

  // 创建观察者
  observer = new IntersectionObserver((entries) => {
    // 如果tab切换锁定中，不处理滚动事件
    if (isTabChangeLocked.value) return;

    const visibleEntries = entries.filter((entry) => entry.isIntersecting);

    if (visibleEntries.length > 0) {
      const sectionId = visibleEntries[0].target.id;
      if (tab.value !== sectionId) {
        // 设置滚动锁定标志，防止触发handleNavigation
        isScrollLocked.value = true;
        tab.value = sectionId;
        // 300ms后解除锁定
        setTimeout(() => {
          isScrollLocked.value = false;
        }, 300);
      }
    }
  }, options);

  // 观察所有section
  tabItems.forEach((item) => {
    const section = document.getElementById(item.value);
    if (section) {
      observer.observe(section);
    }
  });
};

// 接受Android消息，修改settings
const handleAndroidMessage = async (event, msg) => {
  const { type, page, payload } = msg.jsonData;
  console.log("initData修改前", cloneDeep(initData));
  if (type === "params") {
    if (page === 100) {
      if (payload.route) {
        let keyList = payload.route.split(".");
        keyList.shift();
        const keys = keyList;
        const lastKey = keys.pop();
        const value = payload.value;
        let target = initData;
        for (const key of keys) {
          target = target[key];
        }
        target[lastKey] = value;
      }
    }
  }
  console.log("initData修改后", initData);
  await ipc.invoke("global-state:set", "intelligent", initData);
  await getInitialSettings();
};

let handlerId = null;

// 组件挂载后初始化观察者
onMounted(() => {
  getInitialSettings();
  nextTick(() => {
    initIntersectionObserver();
  });

  // 注册WebSocket消息处理函数
  handlerId = registerWsHandler("100", handleAndroidMessage);
});

// 组件卸载前清理观察者
onBeforeUnmount(() => {
  if (observer) {
    observer.disconnect();
    observer = null;
  }

  // 注销WebSocket消息处理函数
  if (handlerId) {
    unregisterWsHandler(handlerId);
  }
});
</script>

<style scoped lang="scss">
.tabs-container {
  width: 200px;
  height: calc(100vh - var(--app-bar-height));
  background-color: rgb(var(--v-theme-surface));
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.intelligence-tabs {
  height: 100%;

  :deep(.v-tab) {
    min-height: 60px;
    height: calc((100vh - var(--app-bar-height)) / 11);
    font-size: 24px;
    justify-content: center;
    padding: 0;
    border-radius: 0;
    transition: background-color 0.3s ease;
    text-orientation: upright;
    font-weight: 500;

    &.v-tab--selected {
      background-color: rgba(var(--v-theme-primary), 0.1);
    }

    &:hover {
      background-color: rgba(var(--v-theme-primary), 0.05);
    }
  }
}

.content-container {
  flex: 1;
  height: calc(100vh - var(--app-bar-height));
  overflow-y: auto;
  background-color: rgb(var(--v-theme-surface));
}

.content-card {
  padding: 20px 32px;
}

/* 区块样式 */
.section-container {
  margin-bottom: 32px;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      font-size: 22px;
      font-weight: bold;
      margin-right: 12px;
    }

    .section-permanent {
      font-size: 16px;
      color: var(--v-theme-on-surface-variant);
      padding: 4px 10px;
      border-radius: 6px;
      border: 1px solid var(--v-theme-outline-variant);
    }
  }

  .section-content {
    padding-left: 20px;
  }
}

/* 设置项样式 */
.setting-item {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .setting-label {
    width: 180px;
    font-size: 22px;
    font-weight: 500;
  }

  .setting-control {
    margin-right: 0;

    :deep(.v-icon) {
      /* 调整图标大小 */
      font-size: 40px;
    }
  }
}

/* 颜色选择器样式 */
.color-options {
  display: flex;
  gap: 12px;

  .color-option {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    border: 2px solid transparent;

    &.selected {
      border-color: var(--v-theme-primary);
    }
  }
}

/* 不透明度控制样式 */
.opacity-control {
  flex: 1;
  font-size: 16px;
}

.scheme-label {
  font-size: 20px;
  font-weight: 500;
  min-width: 50px;
}

.slider-label {
  min-width: 80px;
  font-size: 20px;
}
</style>
