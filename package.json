{"name": "bux-aux", "version": "0.0.66", "description": "bux aux electron", "main": "./public/electron/main.js", "scripts": {"dev": "ee-bin dev", "build": "npm run build-frontend && npm run build-electron && ee-bin encrypt", "start": "ee-bin start", "dev-frontend": "ee-bin dev --serve=frontend", "dev-electron": "ee-bin dev --serve=electron", "build-frontend": "ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist", "build-electron": "ee-bin build --cmds=electron", "encrypt": "ee-bin encrypt", "icon": "ee-bin icon", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "build-w": "ee-bin build --cmds=win64", "build-we": "ee-bin build --cmds=win_e", "build-m": "ee-bin build --cmds=mac", "build-m-arm64": "ee-bin build --cmds=mac_arm64", "build-l": "ee-bin build --cmds=linux", "build-l-arm64": "ee-bin build --cmds=linux_arm64"}, "repository": "https://github.com/dromara/electron-egg.git", "keywords": ["Electron", "electron-egg", "ElectronEgg"], "author": "Chen, Inc <<EMAIL>>", "license": "Apache", "devDependencies": {"@electron/rebuild": "^3.7.1", "@types/node": "^20.16.0", "debug": "^4.4.0", "ee-bin": "^4.0.0", "electron": "^31.7.6", "electron-builder": "^25.1.8"}, "dependencies": {"ee-core": "^4.0.0", "electron-json-storage": "^4.6.0", "electron-updater": "^6.3.8", "iconv-lite": "^0.6.3", "mqtt": "^5.10.3", "prettier": "^3.5.3", "vconsole": "^3.15.1"}}