"use strict";

const WebSocketService = require("../service/websocketService");
const { logger } = require("ee-core/log");
const os = require("os");
/**
 * example
 * @class
 */
class MessageController {
  /**
   * 所有方法接收两个参数
   * @param args 前端传的参数
   * @param event - ipc通信时才有值。详情见：控制器文档
   */

  async node2front(channel, message) {
    mainWindow.webContents.send(channel, message);
  }
  async getParserDict() {
    const { parseConfig } = require("../utils/messageDict");
    return parseConfig;
  }

  async getLocalIP() {
    const interfaces = os.networkInterfaces();
    console.log(interfaces);

    for (const name of Object.keys(interfaces)) {
      for (const netInterface of interfaces[name]) {
        // 跳过内部 IP 和非 IPv4
        if (netInterface.internal || netInterface.family !== "IPv4") continue;
        return netInterface.address;
      }
    }
    return "127.0.0.1"; // 如果没找到合适的 IP，返回 localhost
  }

  async send2Android(args, event) {
    const { communicationType, ...jsonData } = args;
    const body = {
      communicationType: communicationType || 1, // 通信类型 0：应答 1：参数设置命令 2：参数查询命令
      jsonData: jsonData,
    };
    WebSocketService.sendMessageID20(body);
  }

  /**
   * 发送通知消息
   * @param {Object} args - 通知配置
   * @param {string} args.type - 通知类型: 'success', 'info', 'warning', 'error'
   * @param {string} args.content - 通知内容
   * @param {string} args.mode - 显示模式: 'snackbar', 'edge'
   * @param {number} args.duration - 显示时长(毫秒)
   * @param {number} args.count - 显示次数，0表示无限
   * @param {number} args.frequency - 显示频率(毫秒)，0表示立即显示
   * @param {Object} event - IPC事件对象
   */
  async sendNotification(args, event) {
    const { getMainWindow } = require("ee-core/electron");
    const mainWindow = getMainWindow();

    if (mainWindow) {
      mainWindow.webContents.send("show-notification", args);
      logger.info("[messageController] Notification sent:", args);
    } else {
      logger.error("[messageController] Failed to send notification: Main window not available");
    }
  }

  /**
   * 发送加载状态消息
   * @param {Object} args - 加载配置
   * @param {string} args.action - 动作类型: 'show', 'hide', 'updateText'
   * @param {string} args.text - 加载文本
   * @param {string} args.type - 加载类型: 'default', 'circular', 'linear'
   * @param {string} args.color - 加载颜色
   * @param {string} args.textColor - 文本颜色
   * @param {number} args.size - 圆形加载器大小
   * @param {number} args.width - 圆形加载器宽度
   * @param {number} args.height - 线性加载器高度
   * @param {number} args.opacity - 遮罩透明度
   * @param {number} args.zIndex - z-index值
   * @param {string} args.scrimColor - 遮罩颜色
   * @param {boolean} args.customTemplate - 是否使用自定义模板
   * @param {boolean} args.autoHide - 是否自动隐藏
   * @param {number} args.duration - 自动隐藏时间(毫秒)
   * @param {Object} event - IPC事件对象
   */
  async sendLoading(args, event) {
    const { getMainWindow } = require("ee-core/electron");
    const mainWindow = getMainWindow();

    if (mainWindow) {
      mainWindow.webContents.send("show-loading", args);
      logger.info("[messageController] Loading message sent:", args);
    } else {
      logger.error("[messageController] Failed to send loading message: Main window not available");
    }
  }
}

module.exports = MessageController;
