const { spawn } = require("child_process");
const WebSocket = require("ws");
const { logger } = require("ee-core/log");
const globalStateManager = require("./globalStateManager");
const fs = require("fs");

class StreamService {
  constructor() {
    this.wss = null;
    this.ffmpeg = null;
    this.isRunning = false;
    this.reconnectTimer = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000;
    this.hardwareAcceleration = null; // 缓存硬件加速检测结果
  }

  // 检测DRM硬件加速支持
  checkHardwareAcceleration() {
    if (this.hardwareAcceleration !== null) {
      return this.hardwareAcceleration;
    }

    try {
      // 检查 /dev/dri/card0 设备是否存在且可访问
      const drmDevice = '/dev/dri/card0';
      if (fs.existsSync(drmDevice)) {
        try {
          // 尝试检查设备权限
          fs.accessSync(drmDevice, fs.constants.R_OK);
          logger.info("[streamService] DRM device detected and accessible, enabling hardware acceleration");
          this.hardwareAcceleration = 'drm';
          return 'drm';
        } catch (accessError) {
          logger.warn("[streamService] DRM device exists but not accessible:", accessError.message);
        }
      } else {
        logger.info("[streamService] DRM device not found, using software decoding");
      }
    } catch (error) {
      logger.warn("[streamService] Error checking DRM device:", error.message);
    }

    logger.info("[streamService] Using software decoding with multi-threading");
    this.hardwareAcceleration = 'software';
    return 'software';
  }

  // 初始化WebSocket服务器
  initWebSocket() {
    if (this.wss) {
      this.stopWebSocket();
    }

    try {
      this.wss = new WebSocket.Server({ port: 8083, host: '0.0.0.0' }); // 添加host配置
      logger.info("[streamService] WebSocket server started on port 8083");

      this.wss.on("connection", (ws) => {
        logger.info("[streamService] New client connected");

        ws.on("error", (error) => {
          logger.error("[streamService] WebSocket client error:", error);
        });

        ws.on("close", () => {
          logger.info("[streamService] Client disconnected");
        });
      });

      this.wss.on("error", (error) => {
        logger.error("[streamService] WebSocket server error:", error);
        this.stopWebSocket();
      });

    } catch (error) {
      logger.error("[streamService] Failed to start WebSocket server:", error);
      throw error;
    }
  }

  // 停止WebSocket服务器
  stopWebSocket() {
    if (this.wss) {
      this.wss.close(() => {
        logger.info("[streamService] WebSocket server closed");
      });
      this.wss = null;
    }
  }

  // 启动流服务
  startStream() {
    if (this.isRunning) {
      logger.warn("[streamService] Stream is already running");
      return;
    }

    try {
      this.initWebSocket();
      this.startFFmpeg();
      this.isRunning = true;
    } catch (error) {
      logger.error("[streamService] Failed to start stream:", error);
      this.stopStream();
      this.attemptReconnect();
    }
  }

  /* 2025年3月13日
  [
      // 输入控制
      "-rtsp_transport", "tcp",  // 使用TCP传输RTSP流
      "-fflags", "nobuffer",     // 禁用输入缓冲，减少延迟
      "-flags", "low_delay",     // 启用低延迟模式
      "-probesize", "32",        // 减小探测大小，加快启动
      "-analyzeduration", "0",   // 减少分析时间
      "-i", "rtsp://111.56.18.108:28554/live/pro/izypYSw5bi/stream0",  // RTSP流地址
      
      // 视频编码参数
      "-c:v", "mpeg1video",    // 强制MPEG-1编码
      "-b:v", "1000k",         // 目标视频码率
      "-q:v", "5",             // 显式指定量化参数（1-31，值越小质量越高）
      "-bf", "0",              // 禁用B帧减少延迟
      "-vsync", "1",           // 保持源时间戳（0会丢弃/重复帧）
      
      // 分辨率控制
      "-vf", "scale=854:480",  // 确保视频尺寸为2的倍数
      
      // 帧率控制
      "-r", "30",              // 强制输出帧率30fps
      
      // 流优化参数
      "-flags", "+global_header", // 确保全局头信息
      "-muxdelay", "0",         // 最小封装延迟
      "-muxpreload", "0",       // 最小预加载时间
      "-tune", "zerolatency",   // 优化为零延迟模式

      // 音频处理（当前禁用）
      "-an",                   // 禁用音频
      
      // 输出格式
      "-f", "mpegts",          // 强制MPEG-TS输出格式
      
      // 管道输出
      "-"                      // 输出到标准输出
    ]
  */

  // 启动FFmpeg进程
  async startFFmpeg() {
    const rtspUrl = await globalStateManager.get('android.playParams.playUrlVideo');
    console.log("[streamService] rtspUrl:", rtspUrl);
    
    // 检测硬件加速支持
    const hwAccel = this.checkHardwareAcceleration();
    const ffmpegArgs = this.buildFFmpegArgs(rtspUrl, hwAccel);
    
    this.ffmpeg = spawn("ffmpeg", ffmpegArgs);
    
    logger.info(`[streamService] Starting FFmpeg with ${hwAccel} acceleration`);
    logger.info("[streamService] FFmpeg command:", `ffmpeg ${ffmpegArgs.join(' ')}`);
    
    // 设置事件监听器
    this.setupFFmpegListeners();
  }
  
  // 构建FFmpeg参数
  buildFFmpegArgs(rtspUrl, hwAccel) {
    const args = [
      // 输入控制
      "-rtsp_transport", "tcp",  // 使用TCP传输RTSP流
      "-fflags", "nobuffer+flush_packets",  // 增加flush_packets
      "-avioflags", "direct",               // 减少缓冲
      "-flags", "low_delay",     // 启用低延迟模式
      "-probesize", "32",        // 减小探测大小，加快启动
      "-analyzeduration", "0",   // 减少分析时间
    ];
    
    // 根据硬件加速类型添加特定参数
    if (hwAccel === 'drm') {
      // DRM硬件加速参数 - 优化为RK3399，支持H.264和H.265
      args.push(
        "-hwaccel", "drm",           // 使用通用DRM硬件加速
        "-hwaccel_device", "/dev/dri/card0",  // 指定DRM设备
        "-i", rtspUrl,
        
        // 转码参数
        "-c:v", "mpeg1video",      // 转码为MPEG-1
        "-b:v", "1000k",           // 硬件加速支持更高码率
        "-g", "30",                // GOP设置
        "-bf", "0",                // 禁用B帧减少延迟
        "-threads", "1"            // 硬件解码只需要1个线程
      );
    } else {
      // 软件解码参数 - 多线程优化
      args.push(
        "-threads", "4",            // 多线程软解码
        "-i", rtspUrl,
        
        // 软件编码器
        "-c:v", "mpeg1video",      // 软件MPEG-1编码
        "-b:v", "1000k",           // 软解码适中码率
        "-q:v", "3",               // 量化参数
        "-g", "15",                // 更短GOP减少延迟
        "-bf", "0",                // 禁用B帧
        "-threads:v", "4"          // 视频编码线程数
      );
    }
    
    // 通用参数
    args.push(
      "-vsync", "1",             // 保持源时间戳
      "-vf", "scale=854:480",    // 分辨率控制
      "-use_wallclock_as_timestamps", "1", // 使用实时时钟戳
      "-r", "30",                // 强制输出帧率
      "-flags", "+global_header", // 确保全局头信息
      "-muxdelay", "0",           // 最小封装延迟
      "-muxpreload", "0",         // 最小预加载时间
      "-tune", "zerolatency",     // 零延迟优化
      "-an",                     // 禁用音频
      "-f", "mpegts",            // MPEG-TS输出格式
      "-"                        // 输出到标准输出
    );
    
    return args;
  }

  // 设置FFmpeg事件监听器
  setupFFmpegListeners() {
    this.ffmpeg.stdout.on('data', (data) => {
      logger.debug(`[streamService] Sending ${data.length} bytes to clients`);
      
      if (this.wss && this.wss.clients) {
        this.wss.clients.forEach((client) => {
          if (client.readyState === WebSocket.OPEN) {
            client.send(data, { binary: true }); // 确保二进制模式
          }
        });
      }
    });

    this.ffmpeg.stderr.on("data", (data) => {
      // 只记录重要的错误信息，避免日志过多
      const errorMsg = data.toString();
      if (errorMsg.includes('ERROR') || errorMsg.includes('FATAL')) {
        logger.error(`[streamService] FFmpeg Error: ${errorMsg}`);
      }
    });

    this.ffmpeg.on("error", (error) => {
      logger.error("[streamService] FFmpeg process error:", error);
      this.stopStream();
      this.attemptReconnect();
    });

    this.ffmpeg.on("close", (code) => {
      logger.info(`[streamService] FFmpeg process closed with code ${code}`);
      if (this.isRunning) {
        this.stopStream();
        this.attemptReconnect();
      }
    });
  }

  // 停止流服务
  stopStream() {
    this.isRunning = false;

    if (this.ffmpeg) {
      this.ffmpeg.kill();
      this.ffmpeg = null;
    }

    this.stopWebSocket();

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  // 尝试重新连接
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts && !this.reconnectTimer) {
      this.reconnectTimer = setTimeout(() => {
        logger.info(
          `[streamService] Attempting to reconnect (${this.reconnectAttempts + 1}/${
            this.maxReconnectAttempts
          })`
        );
        this.reconnectAttempts++;
        this.startStream();
      }, this.reconnectInterval);
    } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error("[streamService] Max reconnection attempts reached");
      this.reconnectAttempts = 0;
    }
  }

  // 检查服务状态
  isStreamRunning() {
    return this.isRunning;
  }
}

module.exports = new StreamService();
