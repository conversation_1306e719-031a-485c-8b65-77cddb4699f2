import { createApp } from "vue";
import "./style.css";
import App from "./App.vue";

// Vuetify
import "vuetify/styles";
import { createVuetify } from "vuetify";
import * as components from "vuetify/components";
import * as directives from "vuetify/directives";
import "@mdi/font/css/materialdesignicons.css";
import { zhHans, en, ja } from "vuetify/locale";

// i18n
import { i18n } from "./i18n";

// Router
import router from "./router";

import { initVConsole } from "./utils/vconsole";
import { initWsMessageListener } from "@/utils/ipcMessageHandler";

// 初始化 vConsole（仅在开发环境中启用）
initVConsole();
// 初始化WebSocket消息监听
initWsMessageListener();
const localeMap = {
  zh: "zhHans",
  en: "en",
  ja: "ja",
};
const vuetify = createVuetify({
  components,
  directives,
  locale: {
    locale: localeMap[i18n.global.locale.value] || "en",
    fallback: "en",
    messages: { zhHans, en, ja },
  },
  theme: {
    defaultTheme: "dark",
    themes: {
      light: {
        dark: false,
        colors: {
          primary: "#ff5f00",
          secondary: "#ff8f4d",
          background: "#f0f2f5",
          surface: "#f0f2f5",
        },
      },
      dark: {
        dark: true,
        colors: {
          primary: "#ff5f00",
          secondary: "#424242",
          background: "#121212",
          surface: "#212121",
        },
      },
    },
  },
});

const app = createApp(App);
app.use(vuetify);
app.use(i18n);
app.use(router);
app.mount("#app");

