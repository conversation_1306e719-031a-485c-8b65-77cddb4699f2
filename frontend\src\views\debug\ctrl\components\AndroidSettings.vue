<template>
  <v-card class="pa-0">
    <Demo />
    <!-- <DemoLoading /> -->
    <v-card-title class="text-h5 mb-4">Android 设置</v-card-title>
    <v-form ref="form" v-model="isFormValid">
      <!-- 平台地址和端口 -->
      <v-row>
        <v-col class="py-1" cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">管理平台</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.serverHost"
                    label="平台地址"
                    :rules="hostRules"
                    outlined
                    dense
                    hide-details
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.serverPort"
                    label="平台端口"
                    :rules="portRules"
                    outlined
                    dense
                    hide-details
                  />
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 读卡器设置 -->
      <v-row>
        <v-col class="py-1" cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">读卡器</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <Keyboard v-model="formData.cardReader.name" label="名称" outlined dense hide-details />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.cardReader.ip"
                    label="IP地址"
                    :rules="ipRules"
                    outlined
                    dense
                    hide-details
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.cardReader.port"
                    label="端口"
                    :rules="portRules"
                    outlined
                    dense
                    hide-details
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard v-model="formData.cardReader.account" label="账号" outlined dense hide-details />
                </v-col>
                <v-col cols="12" md="6">
                  <Keyboard
                    v-model="formData.cardReader.password"
                    label="密码"
                    outlined
                    dense
                    hide-details
                    type="password"
                  />
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 系统设置 -->
      <v-row>
        <v-col class="py-1" cols="12">
          <v-card class="mb-4" variant="outlined">
            <v-card-title class="text-subtitle-1">系统设置</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>导航栏</span>
                    <v-switch
                      v-model="formData.sysSetting.navigationBar"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span>桌面</span>
                    <v-switch
                      v-model="formData.sysSetting.desktop"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <!-- 2024版本 -->
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span class="text-subtitle-1">2024版本</span>
                    <v-switch
                      v-model="formData.use2024"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
                <!-- 下次开机显示语言 -->
                <v-col cols="12" md="6">
                  <div class="d-flex align-center justify-space-between">
                    <span class="text-subtitle-1">下次开机显示语言</span>
                    <v-switch
                      v-model="formData.showLanguageNext"
                      hide-details
                      density="compact"
                      color="primary"
                      class="setting-control"
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- 操作按钮 -->
      <v-row class="my-1">
        <v-col cols="12" class="d-flex justify-center">
          <v-btn color="error" variant="outlined" class="me-4" @click="resetForm"> 重置 </v-btn>
          <v-btn color="primary" @click="saveForm" :disabled="!isFormValid"> 保存 </v-btn>
        </v-col>
      </v-row>
    </v-form>
  </v-card>

  <v-dialog v-model="dialogShow" width="500">
    <v-alert
      density="compact"
      text="Lorem ipsum dolor sit amet consectetur adipisicing elit. Commodi, ratione debitis quis est labore voluptatibus! Eaque cupiditate minima, at placeat totam, magni doloremque veniam neque porro libero rerum unde voluptatem!"
      title="Alert title"
      type="warning"
    ></v-alert>
  </v-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from "vue";
import Keyboard from "@/components/Keyboard/index.vue";
import { ipc } from "@/utils/ipcRenderer";
import { sendParams2Android, getParamsFromAndroid } from "@/utils/androidMessage";
import { registerWsHandler, unregisterWsHandler } from "@/utils/ipcMessageHandler";
import { findDifferences } from "@/utils/diffUtils";
import Demo from "@/components/GlobalNotification/demo.vue";
import DemoLoading from "@/components/GlobalLoading/demo.vue";

const form = ref(null);
const isFormValid = ref(true);

// 表单数据
const formData = reactive({
  serverHost: "",
  serverPort: "",
  cardReader: {
    name: "builderx",
    ip: "*************",
    port: "8000",
    account: "admin",
    password: "builder@X",
  },
  sysSetting: {
    navigationBar: true,
    desktop: false,
  },
  use2024: false,
  showLanguageNext: true,
});

// 验证规则
const ipRules = [(v) => !!v || "IP地址不能为空", (v) => /^(\d{1,3}\.){3}\d{1,3}$/.test(v) || "IP地址格式不正确"];

const portRules = [
  (v) => !!v || "端口不能为空",
  (v) => /^\d+$/.test(v) || "端口必须是数字",
  (v) => (parseInt(v) >= 0 && parseInt(v) <= 65535) || "端口范围必须在0-65535之间",
];

const hostRules = [(v) => !!v || "主机地址不能为空"];

// 重置表单
const resetForm = () => {
  // 使用深拷贝确保嵌套对象也被正确重置
  Object.assign(formData, JSON.parse(JSON.stringify(initData)));
  form.value?.resetValidation();
};

const dialogShow = ref(false);

// 保存表单
const saveForm = async () => {
  if (!isFormValid.value) return;

  let res = findDifferences(formData, initData);
  console.log("diff", res);
  if (res.length === 0) {
    return;
  }
  // dialogShow.value = true;

  await sendParams2Android("localAndroid", JSON.parse(JSON.stringify(formData)), 301);
  await ipc.invoke("global-state:set", "localAndroid", JSON.parse(JSON.stringify(formData)));

  await getParamsFromAndroid("all", 300);
};

const handleWsMessage = (event, msg) => {
  console.log("收到Android消息:", msg);
};

let handlerId = null;
let initData = null;

// 初始化
onMounted(async () => {
  handlerId = registerWsHandler("*", handleWsMessage);
  try {
    const savedData = await ipc.invoke("global-state:get", "localAndroid");
    initData = JSON.parse(JSON.stringify(savedData));

    if (savedData && Object.keys(savedData).length > 0) {
      Object.assign(formData, savedData);
    }
  } catch (error) {
    console.error("获取Android设置失败:", error);
  }
});

onUnmounted(() => {
  if (handlerId) {
    unregisterWsHandler(handlerId);
  }
});
</script>

<style scoped>
.setting-control {
  max-width: 60px;
}
</style>
