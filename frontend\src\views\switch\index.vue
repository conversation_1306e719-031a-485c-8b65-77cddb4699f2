<template>
  <div class="switch-container" :class="gridClass">
    <div
      v-for="item in switches"
      :key="item.index"
      class="switch-item"
      :class="{ active: item.value }"
      @mousedown.prevent="handleSwitchPress(item)"
      @mouseup="handleSwitchRelease(item)"
      @touchstart.prevent="handleSwitchPress(item)"
      @touchend="handleSwitchRelease(item)"
      @touchmove="handleTouchMove"
    >
      <div class="switch-circle">
        <div class="switch-indicator"></div>
      </div>
      <div class="switch-label">{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { sendParams2Android } from "@/utils/androidMessage";

const switches = ref([]);

const fetchButtonList = async () => {
  const list = await ipc.invoke("global-state:get", "pad.btnList");
  switches.value = list.map((item) => {
    if (item.mode === "press") {
      return { ...item, value: false };
    }
    return item;
  });
};

onMounted(() => {
  fetchButtonList();
});

// 根据开关数量计算网格布局类名
const gridClass = computed(() => {
  const count = switches.value.length;
  switch (count) {
    case 1:
      return "grid-one";
    case 2:
      return "grid-two";
    case 3:
      return "grid-three";
    case 4:
      return "grid-four";
    case 5:
      return "grid-five";
    case 6:
      return "grid-six";
    case 7:
      return "grid-seven";
    case 8:
      return "grid-eight";
    case 9:
      return "grid-nine";
    case 10:
      return "grid-ten";
    case 11:
      return "grid-eleven";
    case 12:
      return "grid-twelve";
    case 13:
      return "grid-thirteen";
    case 14:
      return "grid-fourteen";
    case 15:
      return "grid-fifteen";
    case 16:
      return "grid-sixteen";
    default:
      return "grid-one";
  }
});

// 用于跟踪当前按下的开关及其状态
const pressedSwitch = ref(null);
const isTouchMoved = ref(false);
const longPressTimer = ref(null);
const LONG_PRESS_INTERVAL = 200; // 长按发送数据的间隔时间(毫秒)

// 处理触摸移动事件
const handleTouchMove = () => {
  // 标记为触摸移动，防止滑动误触发
  isTouchMoved.value = true;
};

// 处理开关按下事件
const handleSwitchPress = async (item) => {
  // 重置触摸移动状态
  isTouchMoved.value = false;
  if (item.type === 2) handleFuncSwitch(item);

  // 记录当前按下的开关
  pressedSwitch.value = item;

  // 找到switches数组中的对应项
  const switchItem = switches.value.find((switchItem) => switchItem.index === item.index);

  if (item.mode === "press") {
    // press类型：按下时激活
    switchItem.value = true;
    // 发送初始状态
    console.log(`开关 ${item.index} 按下，状态: ${switchItem.value}`);

    // 设置长按定时器，高频发送状态
    longPressTimer.value = setInterval(() => {
      // 持续发送状态
      console.log(`开关 ${item.index} 长按中，状态: ${switchItem.value}`);
    }, LONG_PRESS_INTERVAL);
  }
  const switchesValue = JSON.parse(JSON.stringify(switches.value));
  await ipc.invoke("global-state:set", "pad.btnList", switchesValue);
};

// 处理开关释放事件
const handleSwitchRelease = async (item) => {
  if (item.type === 2) handleFuncSwitch(item);

  // 清除长按定时器
  if (longPressTimer.value) {
    clearInterval(longPressTimer.value);
    longPressTimer.value = null;
  }

  // 找到switches数组中的对应项
  const switchItem = switches.value.find((switchItem) => switchItem.index === item.index);

  if (item.mode === "press") {
    // press类型：释放时恢复
    switchItem.value = false;
    // 发送释放状态
    console.log(`开关 ${item.index} 释放，状态: ${switchItem.value}`);
  } else if (item.mode === "toggle" && pressedSwitch.value && pressedSwitch.value.index === item.index) {
    // 只要未发生触摸移动，就切换toggle开关状态，不考虑按下时长
    if (!isTouchMoved.value) {
      // toggle类型：切换状态
      switchItem.value = !switchItem.value;
      // 发送切换状态
      console.log(`开关 ${item.index} 切换，状态: ${switchItem.value}`);
    }
  }

  // 释放后重置状态
  pressedSwitch.value = null;
  isTouchMoved.value = false;
  const switchesValue = JSON.parse(JSON.stringify(switches.value));
  await ipc.invoke("global-state:set", "pad.btnList", switchesValue);
};

// 处理功能开关
const handleFuncSwitch = async (item) => {
  sendParams2Android("intelligent." + item.name + ".switch", item.value ? 1 : 0);
};
</script>

<style scoped>
.switch-container {
  height: calc(100vh - var(--app-bar-height));
  padding: 24px;
  display: grid;
  gap: 24px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 一个开关时居中 */
.grid-one {
  grid-template-columns: 1fr;
  justify-items: center;
  align-items: center;
  & .switch-item {
    width: 500px;
  }
}

/* 两个开关时左右排列 */
.grid-two {
  grid-template-columns: 1fr 1fr;
}

/* 三个开关时左中右排列 */
.grid-three {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 四个开关时2x2排列 */
.grid-four {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 五个开关时上面三个下面两个 */
.grid-five {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 六个开关时3x2排列 */
.grid-six {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

/* 七个开关时3x3排列 */
.grid-seven {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

/* 八个开关时3x3排列 */
.grid-eight {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

/* 九个开关时3x3排列 */
.grid-nine {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

/* 十个开关时4x3排列 */
.grid-ten {
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

/* 十一个开关时4x3排列 */
.grid-eleven {
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

/* 十二个开关时4x3排列 */
.grid-twelve {
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

/* 十三个开关时4x4排列 */
.grid-thirteen {
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr;
}

/* 十四个开关时4x4排列 */
.grid-fourteen {
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr;
}

/* 十五个开关时4x4排列 */
.grid-fifteen {
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr;
}

/* 十六个开关时4x4排列 */
.grid-sixteen {
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr;
}

.switch-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background-color: rgb(var(--v-theme-surface));
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  height: 100%;
  width: 100%;
  min-height: 140px;
  max-height: 300px;
  box-sizing: border-box;
  padding: 20px;
  margin: 0 auto;
}

.switch-item.active {
  background-color: rgb(var(--v-theme-primary), 0.7);
  color: white;
}

.switch-circle {
  height: 100%;
  max-height: 100px;
  aspect-ratio: 1;
  border-radius: 50%;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.switch-item.active .switch-circle {
  background-color: rgb(var(--v-theme-primary), 0.6);
}

.switch-indicator {
  width: 60%;
  height: 60%;
  border-radius: 50%;
  background-color: #fff;
  transition: all 0.3s ease;
}

.switch-item.active .switch-indicator {
  background-color: rgb(var(--v-theme-primary), 0.8);
}

.switch-label {
  font-size: 30px;
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
}
</style>
